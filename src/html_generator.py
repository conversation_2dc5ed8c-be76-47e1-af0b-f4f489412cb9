"""
HTML生成模块
负责将处理后的Markdown转换回HTML，恢复原始样式和宏代码块
"""

import re
import json
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class StyleApplier:
    """样式应用器，负责将保存的样式信息重新应用到HTML元素"""
    
    def __init__(self, style_info: Dict):
        self.styles = style_info.get('styles', {})
        self.macros = style_info.get('macros', {})
        self.images = style_info.get('images', {})
    
    def apply_element_style(self, element_id: str, content: str) -> str:
        """为元素应用样式"""
        if element_id not in self.styles:
            return content
        
        style_info = self.styles[element_id]
        tag = style_info['tag']
        style_attr = style_info['style']
        class_attr = style_info['class']
        attributes = style_info['attributes']
        
        # 构建属性字符串
        attr_parts = []
        
        if style_attr:
            attr_parts.append(f'style="{style_attr}"')
        
        if class_attr:
            attr_parts.append(f'class="{class_attr}"')
        
        for attr_name, attr_value in attributes.items():
            if isinstance(attr_value, list):
                attr_value = ' '.join(attr_value)
            attr_parts.append(f'{attr_name}="{attr_value}"')
        
        attr_string = ' ' + ' '.join(attr_parts) if attr_parts else ''
        
        return f'<{tag}{attr_string}>{content}</{tag}>'
    
    def restore_macro(self, macro_id: str) -> str:
        """恢复宏代码块"""
        return self.macros.get(macro_id, f'[{macro_id}]')
    
    def restore_image(self, element_id: str, alt_text: str, src: str) -> str:
        """恢复图片元素"""
        if element_id not in self.images:
            return f'<img src="{src}" alt="{alt_text}"/>'
        
        img_info = self.images[element_id]
        attr_parts = []
        
        for attr_name, attr_value in img_info['attributes'].items():
            attr_parts.append(f'{attr_name}="{attr_value}"')
        
        attr_string = ' ' + ' '.join(attr_parts) if attr_parts else ''
        
        return f'<img{attr_string}/>'


class MarkdownToHTMLConverter:
    """Markdown到HTML转换器"""
    
    def __init__(self, style_info: Dict):
        self.style_applier = StyleApplier(style_info)
    
    def convert(self, markdown_content: str) -> str:
        """将Markdown内容转换为HTML"""
        lines = markdown_content.split('\n')
        html_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                i += 1
                continue
            
            # 处理不同类型的标记
            if line.startswith('[HEADING:'):
                html_lines.append(self._process_heading(line))
            
            elif line.startswith('[TABLE_START:'):
                table_html, consumed_lines = self._process_table(lines[i:])
                html_lines.append(table_html)
                i += consumed_lines - 1
            
            elif line.startswith('[LIST_START:'):
                list_html, consumed_lines = self._process_list(lines[i:])
                html_lines.append(list_html)
                i += consumed_lines - 1
            
            elif line.startswith('[PARA:'):
                html_lines.append(self._process_paragraph(line))
            
            elif line.startswith('[IMAGE:'):
                html_lines.append(self._process_image(line))
            
            elif line.startswith('[MACRO_'):
                html_lines.append(self._process_macro(line))
            
            elif line.startswith('[ELEM:'):
                html_lines.append(self._process_generic_element(line))
            
            else:
                # 普通文本行
                if line.strip():
                    html_lines.append(f'<p>{line}</p>')
            
            i += 1
        
        return '\n'.join(html_lines)
    
    def _process_heading(self, line: str) -> str:
        """处理标题"""
        match = re.match(r'\[HEADING:([^\]]+)\](#+)\s*(.+)', line)
        if not match:
            return line
        
        element_id, level_marks, title = match.groups()
        level = len(level_marks)
        
        heading_content = f'<h{level}>{title}</h{level}>'
        return self.style_applier.apply_element_style(element_id, heading_content)
    
    def _process_table(self, lines: List[str]) -> tuple:
        """处理表格"""
        table_start_match = re.match(r'\[TABLE_START:([^\]]+)\]', lines[0])
        if not table_start_match:
            return lines[0], 1
        
        element_id = table_start_match.group(1)
        table_rows = []
        consumed_lines = 1
        
        # 查找表格内容
        for i in range(1, len(lines)):
            line = lines[i].strip()
            consumed_lines += 1
            
            if line.startswith('[TABLE_END:'):
                break
            
            if '|' in line:
                # 处理表格行
                cells = [cell.strip() for cell in line.split('|')]
                row_html = self._process_table_row(cells)
                table_rows.append(row_html)
        
        # 构建完整表格
        tbody_content = '\n'.join(table_rows)
        table_content = f'<tbody>{tbody_content}</tbody>'
        
        return self.style_applier.apply_element_style(element_id, table_content), consumed_lines
    
    def _process_table_row(self, cells: List[str]) -> str:
        """处理表格行"""
        processed_cells = []
        
        for cell in cells:
            # 提取单元格ID和内容
            cell_match = re.match(r'\[([^\]]+)\](.+)', cell)
            if cell_match:
                cell_id, cell_content = cell_match.groups()
                
                # 判断是表头还是普通单元格
                if cell_content.strip() in ['序号', '任务类型', '任务进展', '编号', '问题描述', '排查进展', '责任人', '当前状态']:
                    cell_html = f'<th>{cell_content}</th>'
                else:
                    cell_html = f'<td>{cell_content}</td>'
                
                styled_cell = self.style_applier.apply_element_style(cell_id, cell_html)
                processed_cells.append(styled_cell)
            else:
                processed_cells.append(f'<td>{cell}</td>')
        
        return f'<tr>{"".join(processed_cells)}</tr>'
    
    def _process_list(self, lines: List[str]) -> tuple:
        """处理列表"""
        list_start_match = re.match(r'\[LIST_START:([^:]+):([^\]]+)\]', lines[0])
        if not list_start_match:
            return lines[0], 1
        
        list_type, element_id = list_start_match.groups()
        list_items = []
        consumed_lines = 1
        
        # 查找列表项
        for i in range(1, len(lines)):
            line = lines[i].strip()
            consumed_lines += 1
            
            if line.startswith('[LIST_END:'):
                break
            
            if line.startswith('[ITEM:'):
                item_match = re.match(r'\[ITEM:([^\]]+)\](.+)', line)
                if item_match:
                    item_id, item_content = item_match.groups()
                    item_html = f'<li>{item_content}</li>'
                    styled_item = self.style_applier.apply_element_style(item_id, item_html)
                    list_items.append(styled_item)
        
        # 构建完整列表
        list_tag = 'ol' if list_type == 'OL' else 'ul'
        list_content = f'<{list_tag}>{"".join(list_items)}</{list_tag}>'
        
        return self.style_applier.apply_element_style(element_id, list_content), consumed_lines
    
    def _process_paragraph(self, line: str) -> str:
        """处理段落"""
        match = re.match(r'\[PARA:([^\]]+)\](.+)', line)
        if not match:
            return line
        
        element_id, content = match.groups()
        para_content = f'<p>{content}</p>'
        return self.style_applier.apply_element_style(element_id, para_content)
    
    def _process_image(self, line: str) -> str:
        """处理图片"""
        match = re.match(r'\[IMAGE:([^\]]+)\]!\[([^\]]*)\]\(([^)]+)\)', line)
        if not match:
            return line
        
        element_id, alt_text, src = match.groups()
        return self.style_applier.restore_image(element_id, alt_text, src)
    
    def _process_macro(self, line: str) -> str:
        """处理宏代码块"""
        match = re.match(r'\[([^\]]+)\]', line)
        if not match:
            return line
        
        macro_id = match.group(1)
        return self.style_applier.restore_macro(macro_id)
    
    def _process_generic_element(self, line: str) -> str:
        """处理通用元素"""
        match = re.match(r'\[ELEM:([^\]]+)\](.+)', line)
        if not match:
            return line
        
        element_id, content = match.groups()
        return self.style_applier.apply_element_style(element_id, content)


class HTMLGenerator:
    """HTML生成器主类"""
    
    def __init__(self):
        pass
    
    def generate_html(self, markdown_content: str, style_info: Dict) -> str:
        """生成完整的HTML内容"""
        converter = MarkdownToHTMLConverter(style_info)
        html_content = converter.convert(markdown_content)
        
        # 添加必要的HTML结构（如果需要）
        return self._wrap_html_content(html_content)
    
    def _wrap_html_content(self, content: str) -> str:
        """包装HTML内容，添加必要的结构"""
        # 对于icenter平台，通常不需要完整的HTML文档结构
        # 只需要返回内容部分
        return content
    
    def save_html_file(self, html_content: str, output_path: str):
        """保存HTML文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"HTML文件已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存HTML文件失败: {e}")
            raise


def generate_daily_report_html(markdown_content: str, style_info: Dict) -> str:
    """生成日报HTML的便捷函数"""
    generator = HTMLGenerator()
    return generator.generate_html(markdown_content, style_info)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    test_markdown = """
    [HEADING:ELEM_1]# 智能化工具提效开发进展日报
    
    [PARA:ELEM_2]这是一个测试段落
    
    [TABLE_START:ELEM_3]
    [CELL_1]序号 | [CELL_2]任务类型 | [CELL_3]任务进展
    [CELL_4]1 | [CELL_5]AI数据分析 | [CELL_6]基于工作流的数据分析报告生成
    [TABLE_END:ELEM_3]
    """
    
    test_style_info = {
        'styles': {
            'ELEM_1': {'tag': 'h1', 'style': 'color: blue;', 'class': '', 'attributes': {}},
            'ELEM_2': {'tag': 'p', 'style': '', 'class': '', 'attributes': {}},
            'ELEM_3': {'tag': 'table', 'style': '', 'class': 'test-table', 'attributes': {'width': '100%'}},
        },
        'macros': {},
        'images': {}
    }
    
    try:
        html_result = generate_daily_report_html(test_markdown, test_style_info)
        print("生成的HTML:")
        print(html_result)
    except Exception as e:
        print(f"测试失败: {e}")
