"""
HTML解析模块
负责解析原始HTML文件，提取样式信息和宏代码块，转换为结构化的Markdown格式
"""

import re
import json
from typing import Dict, List, Tuple, Optional
from bs4 import BeautifulSoup, Tag, NavigableString
import logging

logger = logging.getLogger(__name__)


class StyleExtractor:
    """样式提取器，用于提取和保存HTML中的样式信息"""
    
    def __init__(self):
        self.styles = {}
        self.macro_blocks = {}
        self.images = {}
    
    def extract_inline_styles(self, element: Tag) -> str:
        """提取元素的内联样式"""
        return element.get('style', '')
    
    def extract_classes(self, element: Tag) -> str:
        """提取元素的class属性"""
        classes = element.get('class', [])
        return ' '.join(classes) if isinstance(classes, list) else classes
    
    def save_element_style(self, element_id: str, element: Tag) -> Dict:
        """保存元素的样式信息"""
        style_info = {
            'tag': element.name,
            'style': self.extract_inline_styles(element),
            'class': self.extract_classes(element),
            'attributes': {}
        }
        
        # 保存重要属性
        important_attrs = ['width', 'height', 'align', 'valign', 'rowspan', 'colspan', 'data-sort', 'data-sort-type']
        for attr in important_attrs:
            if element.has_attr(attr):
                style_info['attributes'][attr] = element[attr]
        
        self.styles[element_id] = style_info
        return style_info


class MacroExtractor:
    """宏代码块提取器，用于识别和保存icenter平台的宏代码"""
    
    def __init__(self):
        self.macros = {}
        self.macro_counter = 0
    
    def is_macro_element(self, element: Tag) -> bool:
        """判断是否为宏代码块"""
        if not isinstance(element, Tag):
            return False
        
        # 检查是否包含宏相关的class或属性
        macro_indicators = [
            'insert-macro',
            'icenter-macro',
            'macro-container',
            'editor-block-macro',
            'editor-inline-macro'
        ]
        
        classes = element.get('class', [])
        if isinstance(classes, list):
            classes = ' '.join(classes)
        
        return any(indicator in classes for indicator in macro_indicators)
    
    def extract_macro(self, element: Tag) -> str:
        """提取宏代码块并返回占位符"""
        self.macro_counter += 1
        macro_id = f"MACRO_{self.macro_counter}"
        
        # 保存完整的宏代码块
        self.macros[macro_id] = str(element)
        
        return f"[{macro_id}]"


class HTMLParser:
    """HTML解析器主类"""
    
    def __init__(self):
        self.style_extractor = StyleExtractor()
        self.macro_extractor = MacroExtractor()
        self.element_counter = 0
    
    def parse_html_file(self, file_path: str) -> Tuple[str, Dict]:
        """解析HTML文件，返回Markdown内容和样式信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            return self.parse_html_content(html_content)
        
        except Exception as e:
            logger.error(f"解析HTML文件失败: {e}")
            raise
    
    def parse_html_content(self, html_content: str) -> Tuple[str, Dict]:
        """解析HTML内容，返回Markdown内容和样式信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取主要内容
        markdown_content = self._convert_to_markdown(soup)
        
        # 整理样式和宏信息
        style_info = {
            'styles': self.style_extractor.styles,
            'macros': self.macro_extractor.macros,
            'images': self.style_extractor.images
        }
        
        return markdown_content, style_info
    
    def _convert_to_markdown(self, soup: BeautifulSoup) -> str:
        """将HTML转换为Markdown格式"""
        markdown_lines = []
        
        # 处理根级元素
        for element in soup.children:
            if isinstance(element, Tag):
                md_content = self._process_element(element)
                if md_content.strip():
                    markdown_lines.append(md_content)
        
        return '\n\n'.join(markdown_lines)
    
    def _process_element(self, element: Tag) -> str:
        """处理单个HTML元素"""
        if self.macro_extractor.is_macro_element(element):
            return self.macro_extractor.extract_macro(element)
        
        # 生成元素ID并保存样式
        self.element_counter += 1
        element_id = f"ELEM_{self.element_counter}"
        self.style_extractor.save_element_style(element_id, element)
        
        # 根据标签类型转换
        if element.name == 'table':
            return self._process_table(element, element_id)
        elif element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            return self._process_heading(element, element_id)
        elif element.name == 'p':
            return self._process_paragraph(element, element_id)
        elif element.name in ['ol', 'ul']:
            return self._process_list(element, element_id)
        elif element.name == 'img':
            return self._process_image(element, element_id)
        else:
            return self._process_generic_element(element, element_id)
    
    def _process_table(self, table: Tag, element_id: str) -> str:
        """处理表格元素"""
        markdown_lines = [f"[TABLE_START:{element_id}]"]
        
        tbody = table.find('tbody')
        if tbody:
            rows = tbody.find_all('tr')
        else:
            rows = table.find_all('tr')
        
        for i, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            cell_contents = []
            
            for cell in cells:
                # 保存单元格样式
                self.element_counter += 1
                cell_id = f"CELL_{self.element_counter}"
                self.style_extractor.save_element_style(cell_id, cell)
                
                # 提取单元格内容
                cell_text = self._extract_text_content(cell)
                cell_contents.append(f"[{cell_id}]{cell_text}")
            
            markdown_lines.append(" | ".join(cell_contents))
        
        markdown_lines.append(f"[TABLE_END:{element_id}]")
        return '\n'.join(markdown_lines)
    
    def _process_heading(self, heading: Tag, element_id: str) -> str:
        """处理标题元素"""
        level = int(heading.name[1])  # h1 -> 1, h2 -> 2, etc.
        text = self._extract_text_content(heading)
        return f"[HEADING:{element_id}]{'#' * level} {text}"
    
    def _process_paragraph(self, paragraph: Tag, element_id: str) -> str:
        """处理段落元素"""
        text = self._extract_text_content(paragraph)
        if text.strip():
            return f"[PARA:{element_id}]{text}"
        return ""
    
    def _process_list(self, list_element: Tag, element_id: str) -> str:
        """处理列表元素"""
        list_type = "OL" if list_element.name == 'ol' else "UL"
        markdown_lines = [f"[LIST_START:{list_type}:{element_id}]"]
        
        items = list_element.find_all('li', recursive=False)
        for item in items:
            self.element_counter += 1
            item_id = f"ITEM_{self.element_counter}"
            self.style_extractor.save_element_style(item_id, item)
            
            text = self._extract_text_content(item)
            markdown_lines.append(f"[ITEM:{item_id}]{text}")
        
        markdown_lines.append(f"[LIST_END:{element_id}]")
        return '\n'.join(markdown_lines)
    
    def _process_image(self, img: Tag, element_id: str) -> str:
        """处理图片元素"""
        src = img.get('src', '')
        alt = img.get('alt', '')
        
        # 保存图片信息
        self.style_extractor.images[element_id] = {
            'src': src,
            'alt': alt,
            'attributes': dict(img.attrs)
        }
        
        return f"[IMAGE:{element_id}]![{alt}]({src})"
    
    def _process_generic_element(self, element: Tag, element_id: str) -> str:
        """处理通用元素"""
        text = self._extract_text_content(element)
        if text.strip():
            return f"[ELEM:{element_id}]{text}"
        return ""
    
    def _extract_text_content(self, element: Tag) -> str:
        """提取元素的文本内容，处理嵌套的宏和样式"""
        content_parts = []
        
        for child in element.children:
            if isinstance(child, NavigableString):
                content_parts.append(str(child).strip())
            elif isinstance(child, Tag):
                if self.macro_extractor.is_macro_element(child):
                    content_parts.append(self.macro_extractor.extract_macro(child))
                else:
                    # 递归处理子元素
                    child_content = self._extract_text_content(child)
                    if child_content.strip():
                        content_parts.append(child_content)
        
        return ' '.join(content_parts)


def parse_daily_report(file_path: str) -> Tuple[str, Dict]:
    """解析日报HTML文件的便捷函数"""
    parser = HTMLParser()
    return parser.parse_html_file(file_path)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    try:
        markdown, styles = parse_daily_report("input_example/example1.html")
        print("Markdown内容:")
        print(markdown[:500] + "..." if len(markdown) > 500 else markdown)
        print(f"\n样式信息: {len(styles['styles'])} 个元素, {len(styles['macros'])} 个宏")
    except Exception as e:
        print(f"测试失败: {e}")
