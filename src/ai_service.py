"""
AI服务模块
负责与大模型API交互，处理日报内容生成和优化
"""

import json
import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class AIServiceBase(ABC):
    """AI服务基类，定义统一接口"""
    
    @abstractmethod
    def generate_daily_report(self, existing_content: str, new_work_content: str) -> str:
        """生成日报内容"""
        pass
    
    @abstractmethod
    def optimize_content(self, content: str, optimization_type: str) -> str:
        """优化内容"""
        pass


class PromptTemplate:
    """提示词模板管理"""
    
    DAILY_REPORT_TEMPLATE = """
你是一个专业的工作日报助手。请根据以下信息更新工作日报：

现有日报内容：
{existing_content}

新增工作内容：
{new_work_content}

请按照以下要求更新日报：
1. 保持原有的结构和格式
2. 将新的工作内容合理地整合到"今日进展"部分
3. 如果有风险或问题，更新"风险预警"部分
4. 根据今日进展，适当调整"明日计划"
5. 保持专业的工作汇报语言风格
6. 确保内容准确、简洁、有条理

请直接返回更新后的日报内容，保持原有的Markdown格式标记。
"""
    
    CONTENT_OPTIMIZATION_TEMPLATE = """
请优化以下工作日报内容，使其更加：
- 专业和规范
- 简洁明了
- 逻辑清晰
- 符合企业汇报标准

原始内容：
{content}

优化类型：{optimization_type}

请返回优化后的内容：
"""
    
    WORK_SUMMARY_TEMPLATE = """
请将以下工作内容整理成规范的日报格式：

工作内容：
{work_content}

请按照以下格式整理：
1. 工作类型/项目名称
2. 具体完成的工作
3. 工作成果或进展
4. 遇到的问题（如有）
5. 下一步计划

返回格式化的工作内容：
"""


class MockAIService(AIServiceBase):
    """模拟AI服务，用于测试和开发"""
    
    def __init__(self):
        self.prompt_template = PromptTemplate()
    
    def generate_daily_report(self, existing_content: str, new_work_content: str) -> str:
        """模拟生成日报内容"""
        logger.info("使用模拟AI服务生成日报内容")
        
        # 简单的模拟逻辑
        if "今日进展" in existing_content:
            # 在现有内容基础上添加新工作
            updated_content = existing_content.replace(
                "今日进展",
                f"今日进展\n\n新增工作内容：{new_work_content}"
            )
        else:
            # 如果没有今日进展部分，添加一个
            updated_content = f"{existing_content}\n\n## 今日进展\n{new_work_content}"
        
        return updated_content
    
    def optimize_content(self, content: str, optimization_type: str = "general") -> str:
        """模拟内容优化"""
        logger.info(f"使用模拟AI服务优化内容，类型：{optimization_type}")
        
        # 简单的优化逻辑
        optimized = content.replace("完成了", "成功完成了")
        optimized = optimized.replace("进行了", "深入进行了")
        optimized = optimized.replace("测试", "全面测试")
        
        return optimized


class OpenAIService(AIServiceBase):
    """OpenAI API服务"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.api_key = api_key
        self.model = model
        self.prompt_template = PromptTemplate()
        
        try:
            import openai
            self.client = openai.OpenAI(api_key=api_key)
        except ImportError:
            logger.error("OpenAI库未安装，请运行: pip install openai")
            raise
    
    def generate_daily_report(self, existing_content: str, new_work_content: str) -> str:
        """使用OpenAI生成日报内容"""
        try:
            prompt = self.prompt_template.DAILY_REPORT_TEMPLATE.format(
                existing_content=existing_content,
                new_work_content=new_work_content
            )
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的工作日报助手。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
    
    def optimize_content(self, content: str, optimization_type: str = "general") -> str:
        """使用OpenAI优化内容"""
        try:
            prompt = self.prompt_template.CONTENT_OPTIMIZATION_TEMPLATE.format(
                content=content,
                optimization_type=optimization_type
            )
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的文档优化助手。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.5
            )
            
            return response.choices[0].message.content.strip()
        
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise


class LocalAIService(AIServiceBase):
    """本地AI服务，支持本地部署的大模型"""
    
    def __init__(self, api_url: str, model_name: str = "local-model"):
        self.api_url = api_url
        self.model_name = model_name
        self.prompt_template = PromptTemplate()
    
    def generate_daily_report(self, existing_content: str, new_work_content: str) -> str:
        """使用本地AI生成日报内容"""
        try:
            import requests
            
            prompt = self.prompt_template.DAILY_REPORT_TEMPLATE.format(
                existing_content=existing_content,
                new_work_content=new_work_content
            )
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 2000,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.api_url}/generate", json=payload)
            response.raise_for_status()
            
            result = response.json()
            return result.get("text", "").strip()
        
        except Exception as e:
            logger.error(f"本地AI API调用失败: {e}")
            raise
    
    def optimize_content(self, content: str, optimization_type: str = "general") -> str:
        """使用本地AI优化内容"""
        try:
            import requests
            
            prompt = self.prompt_template.CONTENT_OPTIMIZATION_TEMPLATE.format(
                content=content,
                optimization_type=optimization_type
            )
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 1500,
                "temperature": 0.5
            }
            
            response = requests.post(f"{self.api_url}/generate", json=payload)
            response.raise_for_status()
            
            result = response.json()
            return result.get("text", "").strip()
        
        except Exception as e:
            logger.error(f"本地AI API调用失败: {e}")
            raise


class AIServiceFactory:
    """AI服务工厂类"""
    
    @staticmethod
    def create_service(service_type: str, **kwargs) -> AIServiceBase:
        """创建AI服务实例"""
        if service_type == "mock":
            return MockAIService()
        elif service_type == "openai":
            api_key = kwargs.get("api_key")
            if not api_key:
                raise ValueError("OpenAI服务需要提供api_key")
            model = kwargs.get("model", "gpt-3.5-turbo")
            return OpenAIService(api_key, model)
        elif service_type == "local":
            api_url = kwargs.get("api_url")
            if not api_url:
                raise ValueError("本地AI服务需要提供api_url")
            model_name = kwargs.get("model_name", "local-model")
            return LocalAIService(api_url, model_name)
        else:
            raise ValueError(f"不支持的AI服务类型: {service_type}")


class ContentProcessor:
    """内容处理器，提供高级的内容处理功能"""
    
    def __init__(self, ai_service: AIServiceBase):
        self.ai_service = ai_service
    
    def process_work_content(self, work_content: str) -> str:
        """处理原始工作内容，使其更适合日报格式"""
        # 简单的预处理
        processed = work_content.strip()
        
        # 分割成条目
        if '\n' in processed:
            items = [item.strip() for item in processed.split('\n') if item.strip()]
            processed = '\n'.join(f"- {item}" for item in items)
        
        return processed
    
    def merge_daily_reports(self, reports: List[str]) -> str:
        """合并多个日报内容"""
        if not reports:
            return ""
        
        if len(reports) == 1:
            return reports[0]
        
        # 简单的合并逻辑，实际可以使用AI来智能合并
        merged_content = "合并的日报内容：\n\n"
        for i, report in enumerate(reports, 1):
            merged_content += f"## 日报 {i}\n{report}\n\n"
        
        return merged_content
    
    def extract_key_points(self, content: str) -> List[str]:
        """提取内容要点"""
        # 简单的要点提取逻辑
        lines = content.split('\n')
        key_points = []
        
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('*') or line.startswith('1.')):
                key_points.append(line)
        
        return key_points


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 测试模拟AI服务
    ai_service = AIServiceFactory.create_service("mock")
    processor = ContentProcessor(ai_service)
    
    existing_content = """
    ## 今日进展
    - 完成了数据分析功能开发
    - 进行了单元测试
    """
    
    new_work_content = "完成了新的报告生成功能，修复了2个bug"
    
    try:
        result = ai_service.generate_daily_report(existing_content, new_work_content)
        print("生成的日报内容:")
        print(result)
        
        optimized = ai_service.optimize_content(result)
        print("\n优化后的内容:")
        print(optimized)
        
    except Exception as e:
        print(f"测试失败: {e}")
