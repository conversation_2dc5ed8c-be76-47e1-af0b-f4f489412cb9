"""
Markdown处理模块
负责处理Markdown格式的日报内容，与AI大模型交互生成新内容
"""

import re
import json
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class MarkdownStructure:
    """Markdown结构分析器"""
    
    def __init__(self):
        self.sections = {}
        self.content_blocks = []
    
    def parse_structure(self, markdown_content: str) -> Dict:
        """解析Markdown结构，识别各个部分"""
        lines = markdown_content.split('\n')
        current_section = None
        current_content = []
        
        structure = {
            'title': '',
            'sections': {},
            'metadata': {
                'has_risk_warning': False,
                'has_progress_table': False,
                'has_macro_blocks': False
            }
        }
        
        for line in lines:
            line = line.strip()
            
            # 识别标题
            if line.startswith('[HEADING:'):
                if current_section and current_content:
                    structure['sections'][current_section] = '\n'.join(current_content)
                
                # 提取标题信息
                heading_match = re.match(r'\[HEADING:([^\]]+)\](#+)\s*(.+)', line)
                if heading_match:
                    element_id, level, title = heading_match.groups()
                    
                    # 识别不同类型的标题
                    title_lower = title.lower()
                    if '风险预警' in title or '问题预警' in title:
                        current_section = 'risk_warning'
                        structure['metadata']['has_risk_warning'] = True
                    elif '整体计划' in title or '总体进展' in title:
                        current_section = 'overall_progress'
                    elif '今日' in title or '当前' in title:
                        current_section = 'today_progress'
                    elif '明日' in title or '计划' in title:
                        current_section = 'tomorrow_plan'
                    elif '需求列表' in title:
                        current_section = 'requirements'
                    elif '问题列表' in title:
                        current_section = 'issues'
                    else:
                        current_section = f'section_{len(structure["sections"])}'
                    
                    current_content = [line]
                else:
                    current_content.append(line)
            
            # 识别表格
            elif line.startswith('[TABLE_START:'):
                structure['metadata']['has_progress_table'] = True
                current_content.append(line)
            
            # 识别宏块
            elif line.startswith('[MACRO_'):
                structure['metadata']['has_macro_blocks'] = True
                current_content.append(line)
            
            else:
                current_content.append(line)
        
        # 保存最后一个部分
        if current_section and current_content:
            structure['sections'][current_section] = '\n'.join(current_content)
        
        return structure


class ContentExtractor:
    """内容提取器，从结构化Markdown中提取可编辑的内容"""
    
    def extract_editable_content(self, structure: Dict) -> Dict:
        """提取可编辑的内容部分"""
        editable_content = {
            'risk_warning': '',
            'today_progress': '',
            'tomorrow_plan': '',
            'work_items': []
        }
        
        # 提取风险预警内容
        if 'risk_warning' in structure['sections']:
            editable_content['risk_warning'] = self._extract_text_from_section(
                structure['sections']['risk_warning']
            )
        
        # 提取今日进展
        if 'today_progress' in structure['sections']:
            editable_content['today_progress'] = self._extract_work_items(
                structure['sections']['today_progress']
            )
        
        # 提取明日计划
        if 'tomorrow_plan' in structure['sections']:
            editable_content['tomorrow_plan'] = self._extract_text_from_section(
                structure['sections']['tomorrow_plan']
            )
        
        return editable_content
    
    def _extract_text_from_section(self, section_content: str) -> str:
        """从章节中提取纯文本内容"""
        lines = section_content.split('\n')
        text_lines = []
        
        for line in lines:
            # 跳过标记行
            if line.startswith('[') and ']:' in line:
                continue
            
            # 提取文本内容
            if line.strip():
                # 移除元素标记
                clean_line = re.sub(r'\[ELEM:[^\]]+\]', '', line)
                clean_line = re.sub(r'\[PARA:[^\]]+\]', '', clean_line)
                text_lines.append(clean_line.strip())
        
        return '\n'.join(text_lines)
    
    def _extract_work_items(self, section_content: str) -> List[Dict]:
        """从进展部分提取工作项"""
        work_items = []
        lines = section_content.split('\n')
        
        current_item = None
        in_table = False
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('[TABLE_START:'):
                in_table = True
                continue
            elif line.startswith('[TABLE_END:'):
                in_table = False
                continue
            
            if in_table and '|' in line:
                # 解析表格行
                cells = [cell.strip() for cell in line.split('|')]
                if len(cells) >= 3:
                    # 移除单元格标记
                    task_type = re.sub(r'\[CELL:[^\]]+\]', '', cells[1]).strip()
                    task_progress = re.sub(r'\[CELL:[^\]]+\]', '', cells[2]).strip()
                    
                    if task_type and task_progress and task_type != '任务类型':
                        work_items.append({
                            'type': task_type,
                            'progress': task_progress
                        })
        
        return work_items


class MarkdownProcessor:
    """Markdown处理器主类"""
    
    def __init__(self):
        self.structure_parser = MarkdownStructure()
        self.content_extractor = ContentExtractor()
    
    def process_daily_report(self, markdown_content: str, new_work_content: str) -> str:
        """处理日报内容，结合新的工作内容"""
        # 解析现有结构
        structure = self.structure_parser.parse_structure(markdown_content)
        
        # 提取可编辑内容
        editable_content = self.content_extractor.extract_editable_content(structure)
        
        # 准备AI处理的内容
        ai_input = self._prepare_ai_input(editable_content, new_work_content)
        
        # 这里应该调用AI服务，暂时返回处理后的内容
        processed_content = self._simulate_ai_processing(ai_input, structure)
        
        return processed_content
    
    def _prepare_ai_input(self, editable_content: Dict, new_work_content: str) -> Dict:
        """准备发送给AI的输入内容"""
        return {
            'existing_content': editable_content,
            'new_work_content': new_work_content,
            'instruction': """
            请根据现有的日报内容和新的工作内容，更新日报。要求：
            1. 保持原有的结构和格式
            2. 将新的工作内容合理地整合到相应的部分
            3. 更新今日进展部分
            4. 如有需要，更新明日计划
            5. 保持专业的工作汇报语言风格
            """
        }
    
    def _simulate_ai_processing(self, ai_input: Dict, original_structure: Dict) -> str:
        """模拟AI处理过程（实际应该调用AI服务）"""
        # 这是一个简化的模拟实现
        # 实际应该调用 ai_service.py 中的方法
        
        existing = ai_input['existing_content']
        new_content = ai_input['new_work_content']
        
        # 简单的内容更新逻辑
        updated_structure = original_structure.copy()
        
        # 更新今日进展
        if 'today_progress' in updated_structure['sections']:
            # 在现有内容基础上添加新内容
            current_progress = updated_structure['sections']['today_progress']
            
            # 查找表格结束位置，插入新的工作项
            lines = current_progress.split('\n')
            new_lines = []
            
            for i, line in enumerate(lines):
                new_lines.append(line)
                
                # 在表格中添加新的工作项
                if line.startswith('[TABLE_END:') and new_content:
                    # 在表格结束前插入新的行
                    new_lines.insert(-1, f"[CELL_NEW]新增工作 | [CELL_NEW]{new_content}")
            
            updated_structure['sections']['today_progress'] = '\n'.join(new_lines)
        
        # 重新组装Markdown
        result_lines = []
        for section_name, section_content in updated_structure['sections'].items():
            result_lines.append(section_content)
            result_lines.append('')  # 添加空行分隔
        
        return '\n'.join(result_lines)
    
    def extract_content_for_ai(self, markdown_content: str) -> str:
        """提取适合发送给AI的简化内容"""
        structure = self.structure_parser.parse_structure(markdown_content)
        editable_content = self.content_extractor.extract_editable_content(structure)
        
        # 构建简化的文本内容
        simplified_content = []
        
        if editable_content['risk_warning']:
            simplified_content.append(f"风险预警：{editable_content['risk_warning']}")
        
        if editable_content['work_items']:
            simplified_content.append("今日进展：")
            for item in editable_content['work_items']:
                simplified_content.append(f"- {item['type']}: {item['progress']}")
        
        if editable_content['tomorrow_plan']:
            simplified_content.append(f"明日计划：{editable_content['tomorrow_plan']}")
        
        return '\n\n'.join(simplified_content)
    
    def merge_ai_response(self, original_markdown: str, ai_response: str) -> str:
        """将AI响应合并回原始Markdown结构"""
        # 解析AI响应，提取更新的内容
        # 这里需要根据AI响应的格式来实现具体的合并逻辑
        
        # 暂时返回简单的合并结果
        return f"{original_markdown}\n\n<!-- AI更新内容 -->\n{ai_response}"


def process_daily_report_content(markdown_content: str, new_work_content: str) -> str:
    """处理日报内容的便捷函数"""
    processor = MarkdownProcessor()
    return processor.process_daily_report(markdown_content, new_work_content)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    test_markdown = """
    [HEADING:ELEM_1]# 智能化工具提效开发进展日报
    
    [HEADING:ELEM_2]## 一、当前风险预警
    [PARA:ELEM_3]暂无
    
    [HEADING:ELEM_4]## 三、今日进展
    [TABLE_START:ELEM_5]
    [CELL_1]序号 | [CELL_2]任务类型 | [CELL_3]任务进展
    [CELL_4]1 | [CELL_5]AI数据分析 | [CELL_6]基于工作流的数据分析报告生成穿刺调通
    [TABLE_END:ELEM_5]
    """
    
    new_content = "完成了新的功能开发和测试"
    
    try:
        result = process_daily_report_content(test_markdown, new_content)
        print("处理结果:")
        print(result)
    except Exception as e:
        print(f"测试失败: {e}")
