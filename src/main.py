"""
日报生成工具主程序
整合所有模块，提供完整的日报生成功能
"""

import os
import sys
import argparse
import logging
from typing import Optional, Dict, Any
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from html_parser import HTMLParser
from markdown_processor import MarkdownProcessor
from html_generator import HTMLGenerator
from ai_service import AIServiceFactory, ContentProcessor

logger = logging.getLogger(__name__)


class DailyReportGenerator:
    """日报生成器主类"""

    def __init__(self, ai_service_config: Dict[str, Any]):
        """
        初始化日报生成器

        Args:
            ai_service_config: AI服务配置
                {
                    "type": "mock|openai|local",
                    "api_key": "...",  # for openai
                    "api_url": "...",  # for local
                    "model": "..."     # model name
                }
        """
        self.html_parser = HTMLParser()
        self.markdown_processor = MarkdownProcessor()
        self.html_generator = HTMLGenerator()

        # 初始化AI服务
        service_type = ai_service_config.pop('type', 'mock')
        self.ai_service = AIServiceFactory.create_service(service_type, **ai_service_config)
        self.content_processor = ContentProcessor(self.ai_service)

        logger.info(f"日报生成器初始化完成，AI服务类型: {service_type}")

    def generate_from_template(self, template_path: str, new_work_content: str, output_path: str) -> str:
        """
        基于模板生成新的日报

        Args:
            template_path: 模板HTML文件路径
            new_work_content: 新的工作内容
            output_path: 输出HTML文件路径

        Returns:
            生成的HTML内容
        """
        try:
            logger.info(f"开始生成日报，模板: {template_path}")

            # 第一步：解析HTML模板
            logger.info("步骤1: 解析HTML模板")
            markdown_content, style_info = self.html_parser.parse_html_file(template_path)
            logger.info(f"解析完成，提取了 {len(style_info['styles'])} 个样式元素")

            # 第二步：处理工作内容
            logger.info("步骤2: 处理新工作内容")
            processed_work_content = self.content_processor.process_work_content(new_work_content)

            # 第三步：使用AI生成新的日报内容
            logger.info("步骤3: 使用AI生成新日报内容")
            existing_content = self.markdown_processor.extract_content_for_ai(markdown_content)
            updated_markdown = self.ai_service.generate_daily_report(existing_content, processed_work_content)

            # 第四步：将AI响应合并回原始结构
            logger.info("步骤4: 合并AI响应到原始结构")
            final_markdown = self.markdown_processor.merge_ai_response(markdown_content, updated_markdown)

            # 第五步：生成HTML
            logger.info("步骤5: 生成最终HTML")
            final_html = self.html_generator.generate_html(final_markdown, style_info)

            # 第六步：保存文件
            logger.info("步骤6: 保存输出文件")
            self.html_generator.save_html_file(final_html, output_path)

            logger.info(f"日报生成完成，输出文件: {output_path}")
            return final_html

        except Exception as e:
            logger.error(f"生成日报失败: {e}")
            raise

    def generate_from_scratch(self, work_content: str, output_path: str, template_type: str = "standard") -> str:
        """
        从零开始生成日报

        Args:
            work_content: 工作内容
            output_path: 输出文件路径
            template_type: 模板类型

        Returns:
            生成的HTML内容
        """
        try:
            logger.info("开始从零生成日报")

            # 使用预定义模板结构
            template_structure = self._get_template_structure(template_type)

            # 处理工作内容
            processed_content = self.content_processor.process_work_content(work_content)

            # 使用AI生成完整日报
            full_content = self.ai_service.generate_daily_report(template_structure, processed_content)

            # 创建基本样式信息
            basic_style_info = self._create_basic_style_info()

            # 生成HTML
            html_content = self.html_generator.generate_html(full_content, basic_style_info)

            # 保存文件
            self.html_generator.save_html_file(html_content, output_path)

            logger.info(f"从零生成日报完成，输出文件: {output_path}")
            return html_content

        except Exception as e:
            logger.error(f"从零生成日报失败: {e}")
            raise

    def optimize_existing_report(self, input_path: str, output_path: str, optimization_type: str = "general") -> str:
        """
        优化现有日报

        Args:
            input_path: 输入HTML文件路径
            output_path: 输出HTML文件路径
            optimization_type: 优化类型

        Returns:
            优化后的HTML内容
        """
        try:
            logger.info(f"开始优化现有日报: {input_path}")

            # 解析现有日报
            markdown_content, style_info = self.html_parser.parse_html_file(input_path)

            # 提取内容进行优化
            content_for_optimization = self.markdown_processor.extract_content_for_ai(markdown_content)

            # 使用AI优化内容
            optimized_content = self.ai_service.optimize_content(content_for_optimization, optimization_type)

            # 合并优化后的内容
            final_markdown = self.markdown_processor.merge_ai_response(markdown_content, optimized_content)

            # 生成HTML
            final_html = self.html_generator.generate_html(final_markdown, style_info)

            # 保存文件
            self.html_generator.save_html_file(final_html, output_path)

            logger.info(f"日报优化完成，输出文件: {output_path}")
            return final_html

        except Exception as e:
            logger.error(f"优化日报失败: {e}")
            raise

    def _get_template_structure(self, template_type: str) -> str:
        """获取模板结构"""
        templates = {
            "standard": """
            # 工作日报

            ## 一、风险预警
            暂无

            ## 二、今日进展
            [待填充工作内容]

            ## 三、明日计划
            [根据今日进展制定明日计划]

            ## 四、问题列表
            暂无
            """,

            "detailed": """
            # 项目进展日报

            ## 一、当前风险预警
            暂无

            ## 二、整体计划&进展
            [项目整体进展概述]

            ## 三、今日进展
            [详细工作内容]

            ## 四、明日计划
            [明日工作安排]

            ## 五、需求列表
            [相关需求]

            ## 六、问题列表
            [遇到的问题及解决方案]
            """
        }

        return templates.get(template_type, templates["standard"])

    def _create_basic_style_info(self) -> Dict:
        """创建基本样式信息"""
        return {
            'styles': {
                'default_heading': {
                    'tag': 'h1',
                    'style': 'font-size: 20px; font-weight: bold; color: rgb(51, 51, 51);',
                    'class': '',
                    'attributes': {}
                },
                'default_paragraph': {
                    'tag': 'p',
                    'style': 'line-height: 1.5em;',
                    'class': '',
                    'attributes': {}
                }
            },
            'macros': {},
            'images': {}
        }


def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('daily_report_generator.log', encoding='utf-8')
        ]
    )


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
        return {
            "ai_service": {
                "type": "mock"
            }
        }
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI日报生成工具")
    parser.add_argument("--mode", choices=["template", "scratch", "optimize"],
                       default="template", help="生成模式")
    parser.add_argument("--template", help="模板HTML文件路径")
    parser.add_argument("--input", help="输入HTML文件路径（优化模式）")
    parser.add_argument("--output", required=True, help="输出HTML文件路径")
    parser.add_argument("--content", help="新工作内容")
    parser.add_argument("--content-file", help="工作内容文件路径")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--log-level", default="INFO", help="日志级别")
    parser.add_argument("--template-type", default="standard", help="模板类型（从零生成时）")
    parser.add_argument("--optimization-type", default="general", help="优化类型")

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.log_level)

    try:
        # 加载配置
        config = load_config(args.config)

        # 获取工作内容
        work_content = ""
        if args.content:
            work_content = args.content
        elif args.content_file:
            with open(args.content_file, 'r', encoding='utf-8') as f:
                work_content = f.read()

        # 创建生成器
        generator = DailyReportGenerator(config.get("ai_service", {"type": "mock"}))

        # 根据模式执行相应操作
        if args.mode == "template":
            if not args.template:
                logger.error("模板模式需要指定 --template 参数")
                return 1
            if not work_content:
                logger.error("需要提供工作内容（--content 或 --content-file）")
                return 1

            generator.generate_from_template(args.template, work_content, args.output)

        elif args.mode == "scratch":
            if not work_content:
                logger.error("需要提供工作内容（--content 或 --content-file）")
                return 1

            generator.generate_from_scratch(work_content, args.output, args.template_type)

        elif args.mode == "optimize":
            if not args.input:
                logger.error("优化模式需要指定 --input 参数")
                return 1

            generator.optimize_existing_report(args.input, args.output, args.optimization_type)

        logger.info("操作完成！")
        return 0

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
