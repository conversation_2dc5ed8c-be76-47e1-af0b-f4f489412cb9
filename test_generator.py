#!/usr/bin/env python3
"""
测试脚本 - 验证日报生成工具的功能
"""

import os
import sys
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import DailyReportGenerator

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试AI日报生成工具基本功能")
    print("=" * 60)
    
    # 使用模拟AI服务进行测试
    ai_config = {"type": "mock"}
    
    try:
        # 创建生成器
        generator = DailyReportGenerator(ai_config)
        print("✓ 日报生成器初始化成功")
        
        # 测试基于模板生成
        template_path = "input_example/example1.html"
        if os.path.exists(template_path):
            new_work_content = """
            1. 完成了AI数据分析报告生成功能的优化
            2. 修复了Markdown转HTML的格式问题
            3. 新增了对stack类型图表的支持
            4. 完成了代码单元测试
            """
            
            output_path = "test_output.html"
            
            print(f"✓ 开始测试模板生成功能...")
            html_result = generator.generate_from_template(
                template_path, 
                new_work_content, 
                output_path
            )
            
            if os.path.exists(output_path):
                print(f"✓ 模板生成测试成功，输出文件: {output_path}")
                print(f"  生成的HTML长度: {len(html_result)} 字符")
            else:
                print("✗ 模板生成测试失败，输出文件未创建")
        else:
            print(f"✗ 模板文件不存在: {template_path}")
        
        # 测试从零生成
        print(f"✓ 开始测试从零生成功能...")
        scratch_content = """
        今日完成的工作：
        1. 开发了新的日报生成工具
        2. 实现了HTML解析和Markdown转换功能
        3. 集成了AI服务接口
        4. 编写了完整的测试用例
        """
        
        scratch_output = "test_scratch_output.html"
        html_result = generator.generate_from_scratch(
            scratch_content,
            scratch_output,
            "standard"
        )
        
        if os.path.exists(scratch_output):
            print(f"✓ 从零生成测试成功，输出文件: {scratch_output}")
        else:
            print("✗ 从零生成测试失败")
        
        print("\n" + "=" * 60)
        print("所有基本功能测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_individual_modules():
    """测试各个模块"""
    print("\n" + "=" * 60)
    print("测试各个模块功能")
    print("=" * 60)
    
    # 测试HTML解析模块
    try:
        from html_parser import HTMLParser
        parser = HTMLParser()
        
        template_path = "input_example/example1.html"
        if os.path.exists(template_path):
            markdown, styles = parser.parse_html_file(template_path)
            print(f"✓ HTML解析模块测试成功")
            print(f"  提取的Markdown长度: {len(markdown)} 字符")
            print(f"  提取的样式数量: {len(styles['styles'])}")
            print(f"  提取的宏数量: {len(styles['macros'])}")
        else:
            print(f"✗ 模板文件不存在，跳过HTML解析测试")
            
    except Exception as e:
        print(f"✗ HTML解析模块测试失败: {e}")
    
    # 测试Markdown处理模块
    try:
        from markdown_processor import MarkdownProcessor
        processor = MarkdownProcessor()
        
        test_markdown = """
        [HEADING:ELEM_1]# 测试日报
        [PARA:ELEM_2]这是测试内容
        """
        
        result = processor.extract_content_for_ai(test_markdown)
        print(f"✓ Markdown处理模块测试成功")
        print(f"  提取的内容长度: {len(result)} 字符")
        
    except Exception as e:
        print(f"✗ Markdown处理模块测试失败: {e}")
    
    # 测试AI服务模块
    try:
        from ai_service import AIServiceFactory
        ai_service = AIServiceFactory.create_service("mock")
        
        test_content = "测试内容"
        result = ai_service.generate_daily_report("现有内容", test_content)
        print(f"✓ AI服务模块测试成功")
        print(f"  生成的内容长度: {len(result)} 字符")
        
    except Exception as e:
        print(f"✗ AI服务模块测试失败: {e}")
    
    # 测试HTML生成模块
    try:
        from html_generator import HTMLGenerator
        generator = HTMLGenerator()
        
        test_markdown = "[HEADING:ELEM_1]# 测试标题\n[PARA:ELEM_2]测试段落"
        test_styles = {
            'styles': {
                'ELEM_1': {'tag': 'h1', 'style': '', 'class': '', 'attributes': {}},
                'ELEM_2': {'tag': 'p', 'style': '', 'class': '', 'attributes': {}}
            },
            'macros': {},
            'images': {}
        }
        
        html_result = generator.generate_html(test_markdown, test_styles)
        print(f"✓ HTML生成模块测试成功")
        print(f"  生成的HTML长度: {len(html_result)} 字符")
        
    except Exception as e:
        print(f"✗ HTML生成模块测试失败: {e}")

def show_sample_output():
    """显示示例输出"""
    print("\n" + "=" * 60)
    print("示例输出预览")
    print("=" * 60)
    
    output_files = ["test_output.html", "test_scratch_output.html"]
    
    for file_path in output_files:
        if os.path.exists(file_path):
            print(f"\n--- {file_path} 内容预览 ---")
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 显示前500个字符
                preview = content[:500] + "..." if len(content) > 500 else content
                print(preview)
        else:
            print(f"\n--- {file_path} 文件不存在 ---")

def cleanup_test_files():
    """清理测试文件"""
    test_files = ["test_output.html", "test_scratch_output.html", "daily_report_generator.log"]
    
    print(f"\n清理测试文件...")
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✓ 已删除: {file_path}")
            except Exception as e:
                print(f"✗ 删除失败 {file_path}: {e}")

def main():
    """主测试函数"""
    setup_test_logging()
    
    print("AI日报生成工具 - 功能测试")
    print("开始时间:", logging.Formatter().formatTime(logging.LogRecord(
        name="test", level=logging.INFO, pathname="", lineno=0,
        msg="", args=(), exc_info=None
    )))
    
    try:
        # 测试各个模块
        test_individual_modules()
        
        # 测试基本功能
        test_basic_functionality()
        
        # 显示示例输出
        show_sample_output()
        
        print(f"\n🎉 测试完成！")
        print(f"如果您看到了 ✓ 标记，说明相应功能正常工作。")
        print(f"如果您看到了 ✗ 标记，请检查相应的错误信息。")
        
        # 询问是否清理测试文件
        response = input(f"\n是否清理测试文件？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup_test_files()
        
    except KeyboardInterrupt:
        print(f"\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
