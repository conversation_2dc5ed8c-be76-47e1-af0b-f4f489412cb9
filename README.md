# AI日报生成工具 v3.0

基于AI大模型的工作日报、工作周报生成工具，支持从现有模板生成新日报，保持原有样式和格式。

## 功能特点

- 🎯 **智能内容生成**：基于AI大模型，根据用户输入的工作内容智能生成日报
- 🎨 **样式保持**：完美保留原始HTML的样式、宏代码块和格式
- 🚀 **快速响应**：优化的处理流程，避免token限制问题
- 🔧 **多种模式**：支持模板生成、从零生成、内容优化三种模式
- 🌐 **多AI支持**：支持OpenAI、本地模型等多种AI服务
- 📱 **icenter兼容**：专为icenter平台优化，支持内嵌样式

## 安装说明

1. 克隆项目
```bash
git clone <repository-url>
cd dailyAndWeekly3.0
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置AI服务（可选）
编辑 `config.json` 文件，配置您的AI服务：

```json
{
  "ai_service": {
    "type": "openai",
    "openai_config": {
      "api_key": "your-api-key",
      "model": "gpt-3.5-turbo"
    }
  }
}
```

## 使用方法

### 1. 基于模板生成日报

```bash
python src/main.py --mode template \
  --template input_example/example1.html \
  --content "今天完成了新功能开发和测试" \
  --output output/new_report.html
```

### 2. 从零生成日报

```bash
python src/main.py --mode scratch \
  --content "完成了AI数据分析功能开发，修复了2个bug，进行了单元测试" \
  --output output/new_report.html \
  --template-type standard
```

### 3. 优化现有日报

```bash
python src/main.py --mode optimize \
  --input input_example/example1.html \
  --output output/optimized_report.html \
  --optimization-type general
```

### 4. 使用内容文件

```bash
# 将工作内容写入文件
echo "今天的工作内容..." > work_content.txt

python src/main.py --mode template \
  --template input_example/example1.html \
  --content-file work_content.txt \
  --output output/new_report.html
```

## 项目结构

```
dailyAndWeekly3.0/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── main.py            # 主程序入口
│   ├── html_parser.py     # HTML解析模块
│   ├── markdown_processor.py  # Markdown处理模块
│   ├── html_generator.py  # HTML生成模块
│   └── ai_service.py      # AI服务模块
├── input_example/         # 示例输入文件
│   ├── example1.html
│   └── example2.html
├── output_example/        # 示例输出文件
├── config.json           # 配置文件
├── requirements.txt      # 依赖列表
├── 开发方案.md           # 开发方案文档
└── README.md            # 项目说明
```

## 核心模块说明

### HTML解析模块 (html_parser.py)
- 解析原始HTML文件
- 提取样式信息和宏代码块
- 转换为结构化的Markdown格式
- 保持icenter平台特有的宏代码块

### Markdown处理模块 (markdown_processor.py)
- 分析Markdown结构
- 提取可编辑的内容部分
- 与AI服务交互处理内容
- 合并AI响应到原始结构

### HTML生成模块 (html_generator.py)
- 将Markdown转换回HTML
- 恢复原始样式和宏代码块
- 确保在icenter平台正常显示
- 支持内嵌样式

### AI服务模块 (ai_service.py)
- 支持多种AI服务（OpenAI、本地模型等）
- 优化的提示词模板
- 内容处理和优化功能
- 工厂模式支持扩展

## 配置说明

### AI服务配置

支持三种AI服务类型：

1. **Mock服务**（默认，用于测试）
```json
{
  "ai_service": {
    "type": "mock"
  }
}
```

2. **OpenAI服务**
```json
{
  "ai_service": {
    "type": "openai",
    "openai_config": {
      "api_key": "your-api-key",
      "model": "gpt-3.5-turbo"
    }
  }
}
```

3. **本地AI服务**
```json
{
  "ai_service": {
    "type": "local",
    "local_config": {
      "api_url": "http://localhost:8000",
      "model_name": "local-model"
    }
  }
}
```

## 开发指南

### 添加新的AI服务

1. 继承 `AIServiceBase` 类
2. 实现 `generate_daily_report` 和 `optimize_content` 方法
3. 在 `AIServiceFactory` 中注册新服务

### 扩展模板类型

1. 在 `config.json` 中添加新模板配置
2. 在 `DailyReportGenerator._get_template_structure` 中添加模板结构

### 自定义样式处理

1. 修改 `StyleExtractor` 类以支持新的样式属性
2. 在 `StyleApplier` 中添加相应的样式应用逻辑

## 注意事项

1. **样式保持**：工具会尽力保持原始HTML的样式，但复杂的CSS可能需要手动调整
2. **宏代码块**：icenter平台的宏代码块会被完整保留
3. **Token限制**：大型HTML文件可能需要分段处理以避免AI服务的token限制
4. **编码问题**：确保所有文件使用UTF-8编码

## 故障排除

### 常见问题

1. **AI服务连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API服务地址

2. **样式丢失**
   - 检查原始HTML是否使用内嵌样式
   - 确认没有外部CSS依赖

3. **宏代码块显示异常**
   - 确保宏代码块被正确识别和保存
   - 检查icenter平台兼容性

## 许可证

本项目采用 MIT 许可证。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 更新日志

### v3.0.0
- 重新设计架构，采用模块化设计
- 支持多种AI服务
- 完善的样式保持机制
- 优化的处理流程
- 完整的命令行界面
