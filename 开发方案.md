# 任务
开发一款基于AI大模型的工作日报、工作周生成工具

# 目标
根据用户历史日报结合用户输入的新工作内容进行整理，生成新的日报、或工作周页面，分别实现，先实现日报的生成功能

# 思路

首先分析一下input_example目录下的原始示例文件，该示例文件为用户原始日报页面代码，需要参考原始的示例代码生成一份新的日报页面代码。

# 注意
- 原始的示例文件来源于公司的icenter平台，该平台可以通过web访问创建页面内容，类似于博客，代码中包含有许多用户自定义的样式，其中也有基于公司平台的宏代码块，这些代码块需要保留，不能被修改，同时输出的代码不能有css,js只能使用内嵌样式，否则无法在icenter平台正常显示。
- 处于AI大模型单次输入token的数量限制，不能直接将原始html代码直接给大模型处理，会出现响应慢甚至输出截断的情况。
- 希望输出能够响应快速，输出页面内容尽量保留用户原有的页面样式风格，只更新用户输入的工作内容。

