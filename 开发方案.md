# 任务
开发一款基于AI大模型的工作日报、工作周生成工具

# 目标
根据用户历史日报结合用户输入的新工作内容进行整理，生成新的日报、或工作周页面，分别实现，先实现日报的生成功能

# 思路

首先分析一下input_example目录下的原始示例文件，该示例文件为用户原始日报页面代码，需要参考原始的示例代码生成一份新的日报页面代码。

# 注意
- 原始的示例文件来源于公司的icenter平台，该平台可以通过web访问创建页面内容，类似于博客，代码中包含有许多用户自定义的样式，其中也有基于公司平台的宏代码块，这些代码块需要保留，不能被修改，同时输出的代码不能有css,js只能使用内嵌样式，否则无法在icenter平台正常显示。
- 处于AI大模型单次输入token的数量限制，不能直接将原始html代码直接给大模型处理，会出现响应慢甚至输出截断的情况。
- 希望输出能够响应快速，输出页面内容尽量保留用户原有的页面样式风格，只更新用户输入的工作内容。
- 在无模板的情况下，需要根据用户内容生成新的日报模板内容，此时可以根据用户输入的内容自由生成合适的日报模板。

# 参考方案
1、先将用户原始日志内容转化为markdown格式，同时要考虑如何保留原始样式。
2、将markdown格式的内容发和用户新输入的内容一同发送给大模型，生成新的markdown格式的内容。
3、将新的markdown格式的内容转化为html格式，同时要将原始的样式内容保留。

# 开发计划
## 第一阶段：核心模块开发
1. **HTML解析模块** (`html_parser.py`) - 解析原始HTML并转换为Markdown
2. **Markdown处理模块** (`markdown_processor.py`) - 处理Markdown内容与AI交互
3. **HTML生成模块** (`html_generator.py`) - 将Markdown转换回HTML并恢复样式

## 第二阶段：AI集成模块
4. **AI服务模块** (`ai_service.py`) - 集成大模型API和提示词优化

## 第三阶段：主程序和工具
5. **主程序** (`main.py`) - 整合所有模块提供完整功能
6. **配置和工具模块** - 配置管理、日志记录、错误处理