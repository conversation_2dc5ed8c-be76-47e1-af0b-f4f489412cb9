{"ai_service": {"type": "mock", "comment": "AI服务配置，支持 mock/openai/local 三种类型", "openai_config": {"api_key": "your-openai-api-key-here", "model": "gpt-3.5-turbo"}, "local_config": {"api_url": "http://localhost:8000", "model_name": "local-model"}}, "logging": {"level": "INFO", "file": "daily_report_generator.log"}, "templates": {"standard": {"name": "标准日报模板", "sections": ["风险预警", "今日进展", "明日计划", "问题列表"]}, "detailed": {"name": "详细项目日报模板", "sections": ["风险预警", "整体计划&进展", "今日进展", "明日计划", "需求列表", "问题列表"]}}, "output": {"default_encoding": "utf-8", "preserve_original_styles": true, "include_macro_blocks": true}}