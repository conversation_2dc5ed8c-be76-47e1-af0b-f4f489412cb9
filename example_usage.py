#!/usr/bin/env python3
"""
使用示例 - 演示如何使用AI日报生成工具
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import DailyReportGenerator

def example_template_generation():
    """示例1：基于模板生成日报"""
    print("=" * 60)
    print("示例1：基于模板生成日报")
    print("=" * 60)
    
    # 配置AI服务（使用模拟服务）
    ai_config = {"type": "mock"}
    
    # 创建生成器
    generator = DailyReportGenerator(ai_config)
    
    # 新的工作内容
    new_work_content = """
    今日完成的主要工作：
    
    1. AI数据分析功能优化
       - 完成了VONR语音数据分析报告生成功能
       - 新增支持stack类型图表的绘制
       - 优化了md转html的代码实现
    
    2. 系统测试验证
       - 基于4月份数据验证了报告适配性
       - 运行状态正常，无异常
    
    3. 工作流集成
       - 将本地工作流导入内网dify服务
       - 导入及运行验证均正常
    
    4. 技术交流
       - 与业务人员进行技术交流
       - 同步当前实现效果
       - 讨论后续优化方向
    """
    
    # 生成日报
    try:
        output_path = "example_output.html"
        html_result = generator.generate_from_template(
            "input_example/example1.html",
            new_work_content,
            output_path
        )
        
        print(f"✓ 日报生成成功！")
        print(f"  输出文件：{output_path}")
        print(f"  文件大小：{len(html_result)} 字符")
        
        return output_path
        
    except Exception as e:
        print(f"✗ 生成失败：{e}")
        return None

def example_scratch_generation():
    """示例2：从零生成日报"""
    print("\n" + "=" * 60)
    print("示例2：从零生成日报")
    print("=" * 60)
    
    # 配置AI服务
    ai_config = {"type": "mock"}
    
    # 创建生成器
    generator = DailyReportGenerator(ai_config)
    
    # 工作内容
    work_content = """
    项目进展汇报：
    
    1. 完成了AI日报生成工具的开发
       - 实现了HTML解析和Markdown转换
       - 集成了多种AI服务接口
       - 支持样式保持和宏代码块处理
    
    2. 功能测试
       - 编写了完整的测试用例
       - 验证了各个模块的功能
       - 测试通过率100%
    
    3. 文档编写
       - 完成了README文档
       - 编写了使用说明
       - 提供了配置示例
    
    遇到的问题：
    - 复杂HTML样式处理需要进一步优化
    - AI服务的token限制需要考虑
    
    下一步计划：
    - 优化样式处理算法
    - 增加更多AI服务支持
    - 完善错误处理机制
    """
    
    try:
        output_path = "example_scratch.html"
        html_result = generator.generate_from_scratch(
            work_content,
            output_path,
            "detailed"
        )
        
        print(f"✓ 从零生成成功！")
        print(f"  输出文件：{output_path}")
        print(f"  文件大小：{len(html_result)} 字符")
        
        return output_path
        
    except Exception as e:
        print(f"✗ 生成失败：{e}")
        return None

def example_optimization():
    """示例3：优化现有日报"""
    print("\n" + "=" * 60)
    print("示例3：优化现有日报")
    print("=" * 60)
    
    # 配置AI服务
    ai_config = {"type": "mock"}
    
    # 创建生成器
    generator = DailyReportGenerator(ai_config)
    
    try:
        input_path = "input_example/example1.html"
        output_path = "example_optimized.html"
        
        html_result = generator.optimize_existing_report(
            input_path,
            output_path,
            "professional"
        )
        
        print(f"✓ 日报优化成功！")
        print(f"  输入文件：{input_path}")
        print(f"  输出文件：{output_path}")
        print(f"  文件大小：{len(html_result)} 字符")
        
        return output_path
        
    except Exception as e:
        print(f"✗ 优化失败：{e}")
        return None

def show_file_preview(file_path, max_lines=10):
    """显示文件预览"""
    if not os.path.exists(file_path):
        print(f"文件不存在：{file_path}")
        return
    
    print(f"\n--- {file_path} 预览 ---")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines[:max_lines], 1):
            print(f"{i:2d}: {line.rstrip()}")
        
        if len(lines) > max_lines:
            print(f"... (还有 {len(lines) - max_lines} 行)")
            
    except Exception as e:
        print(f"读取文件失败：{e}")

def main():
    """主函数"""
    print("AI日报生成工具 - 使用示例")
    print("=" * 60)
    
    # 检查依赖
    if not os.path.exists("input_example/example1.html"):
        print("错误：找不到示例模板文件 input_example/example1.html")
        return
    
    generated_files = []
    
    # 示例1：基于模板生成
    file1 = example_template_generation()
    if file1:
        generated_files.append(file1)
    
    # 示例2：从零生成
    file2 = example_scratch_generation()
    if file2:
        generated_files.append(file2)
    
    # 示例3：优化现有日报
    file3 = example_optimization()
    if file3:
        generated_files.append(file3)
    
    # 显示生成的文件预览
    print("\n" + "=" * 60)
    print("生成文件预览")
    print("=" * 60)
    
    for file_path in generated_files:
        show_file_preview(file_path, 5)
    
    # 总结
    print(f"\n" + "=" * 60)
    print("示例运行完成")
    print("=" * 60)
    print(f"成功生成 {len(generated_files)} 个文件：")
    for file_path in generated_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✓ {file_path} ({size} 字节)")
    
    print(f"\n💡 提示：")
    print(f"  - 您可以在浏览器中打开这些HTML文件查看效果")
    print(f"  - 要使用真实的AI服务，请修改config.json中的配置")
    print(f"  - 更多使用方法请参考README.md")

if __name__ == "__main__":
    main()
